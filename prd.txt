📁 專案目錄結構

```
VoiceAppProject/
├── PRD/
│   └── prd.txt
├── Frontend/
│   ├── public/
│   ├── src/
│   │   ├── assets/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/  ← 與 LLM、TTS、STT 通訊邏輯
│   │   └── App.tsx
│   ├── tailwind.config.ts
│   ├── index.html
│   └── vite.config.ts
├── Backend/
│   ├── cmd/
│   │   └── main.go
│   ├── internal/
│   │   ├── handler/    ← HTTP handler
│   │   ├── service/    ← LLM 處理邏輯
│   │   ├── utils/      ← 輔助工具
│   │   └── config/     ← 設定檔管理
│   └── go.mod
```

---

📄 `PRD/prd.txt`

# 語音對話應用產品規格文件（PRD）

## 一、功能需求清單

1. 語音啟動按鈕：觸發語音辨識流程
2. 語音轉文字（STT）：辨識語音並即時轉為文字
3. 顯示語音文字內容於 UI
4. 對話內容送出至 LLM API
5. 顯示 LLM 回應內容於 UI
6. 將 LLM 回應轉為語音播放（TTS）
7. 對話歷程管理（可選擇實作）

## 二、使用者操作流程圖（Mermaid 格式）

```mermaid
flowchart TD
    A[使用者點擊語音按鈕] --> B[語音辨識中]
    B --> C[語音轉文字顯示於畫面]
    C --> D[送文字給 LLM 處理]
    D --> E[顯示 LLM 回覆文字]
    E --> F[語音合成播放 LLM 回覆]
```

## 三、UI/UX 初步構想

- 主介面：
  - 上方為對話歷程區塊（使用者與 AI 對話泡泡）
  - 下方為語音按鈕（圓形麥克風圖示）
  - 狀態提示（辨識中、傳送中、回應中等）

```text
╔══════════════════════════════╗
║ 🤖 Hello! How can I help?     ║
║ 🧑 請幫我查一下台北天氣       ║
║ 🤖 台北今天天氣為...         ║
╚══════════════════════════════╝
       [ 🎤 按住說話 ]
```

## 四、系統架構設計與模組說明

```mermaid
graph LR
    UI -->|語音輸入| STT
    STT -->|文字結果| LLM_REQ
    LLM_REQ -->|請求文字| Backend
    Backend -->|LLM 呼叫| LLM_API
    LLM_API -->|回應文字| Backend
    Backend -->|回應文字| TTS
    TTS -->|語音播放| UI
```

### 模組明細

1. **語音輸入與辨識（STT）模組**：

   - 技術：Web Speech API（前端）或 Whisper API（後端）
   - 功能：錄音 → 文字輸出

2. **LLM 串接模組**：

   - 技術：REST API 呼叫 Gemini / GPT
   - 格式：
     ```json
     {
       "user_input": "請幫我找台北101資訊"
     }
     ```
     回應：
     ```json
     {
       "reply": "台北101是..."
     }
     ```

3. **語音輸出（TTS）模組**：

   - 技術：SpeechSynthesis API（前端）
   - 功能：將文字轉為語音播放

4. **前端控制與對話框 UI 模組**：

   - 技術棧：React + Tailwind CSS
   - 狀態管理：建議使用 `useState`, `useEffect`, `useReducer`

5. **後端邏輯處理模組**：

   - 技術棧：Golang + Gin（API）
   - 功能：轉送 LLM 請求與回應封裝

## 五、LLM API 通訊格式與範例

- 請求端（Frontend -> Backend）

```json
POST /api/message
{
  "message": "今天台北天氣如何？"
}
```

- 回應端

```json
{
  "reply": "今天天氣晴，氣溫25度。"
}
```

## 六、前後端協作流程

```mermaid
sequenceDiagram
  participant U as User
  participant F as Frontend (React)
  participant B as Backend (Go)
  participant L as LLM
  participant T as TTS

  U->>F: 按語音鍵開始說話
  F->>F: 啟動 Web Speech API
  F->>F: 顯示使用者語音文字
  F->>B: 傳送 user input
  B->>L: 傳送至 LLM API
  L-->>B: 回應文字
  B-->>F: 回傳 LLM 結果
  F->>T: 呼叫 SpeechSynthesis API
  T->>U: 播放語音回覆
```

## 七、語音技術建議

| 功能       | 建議技術                       |
| -------- | -------------------------- |
| 語音辨識 STT | Web Speech API / Whisper   |
| 語音合成 TTS | SpeechSynthesis API        |
| LLM      | Google Gemini / OpenAI GPT |
| 前端       | React + Tailwind + Vite    |
| 後端       | Go (Gin Web Framework)     |

---

✅ 開發團隊可依照上述模組逐步實作、並採分層結構利於維護與擴充。

