{"master": {"tasks": [{"id": 1, "title": "專案初始化與結構設定", "description": "根據 PRD 中定義的目錄結構，初始化前後端專案。前端使用 Vite 建立 React + TypeScript 專案，後端使用 Go Modules 建立基礎結構。", "details": "1. 建立根目錄 `VoiceAppProject` 及其子目錄 `Frontend`, `Backend`, `PRD`。\n2. 在 `Frontend` 目錄下，執行 `npm create vite@latest . -- --template react-ts` 來建立 React 專案。\n3. 安裝 Tailwind CSS: `npm install -D tailwindcss postcss autoprefixer && npx tailwindcss init -p`。\n4. 在 `Backend` 目錄下，執行 `go mod init voiceapp/backend` 初始化 Go 模組。\n5. 建立後端目錄結構: `cmd/main.go`, `internal/handler`, `internal/service` 等。", "testStrategy": "驗證前後端目錄結構是否符合 PRD 要求。確認前端專案可以成功啟動 (`npm run dev`)，後端 `go.mod` 檔案已建立。", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "前端 UI 基礎介面建構", "description": "使用 React 和 Tailwind CSS 建立應用的靜態 UI 介面，包括對話歷程顯示區、狀態提示區和底部的語音控制按鈕。", "details": "1. 建立 `src/components/ChatHistory.tsx` 用於顯示對話泡泡。\n2. 建立 `src/components/VoiceButton.tsx` 作為圓形麥克風按鈕。\n3. 建立 `src/components/StatusIndicator.tsx` 用於顯示「辨識中」等狀態。\n4. 在 `src/App.tsx` 中組合以上元件，並使用 Tailwind CSS 根據 PRD 的 UI 構想進行排版與樣式設定。此階段不需實作任何功能邏輯。", "testStrategy": "在瀏覽器中打開前端頁面，目視檢查 UI 佈局是否與 PRD 中的示意圖一致。確認元件在不同螢幕尺寸下能有基本的響應式表現。", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "後端 API 端點基礎建設", "description": "使用 Go 和 Gin 框架搭建後端 HTTP 伺服器，並實作一個 `/api/message` POST 端點用於接收前端訊息。", "details": "1. 在 `main.go` 中，初始化 Gin 引擎：`r := gin.Default()`。\n2. 設定 CORS 中介軟體，允許來自前端開發伺服器的跨域請求。\n3. 在 `internal/handler/message_handler.go` 中定義處理函式 `HandleMessage`。\n4. 註冊路由：`r.POST(\"/api/message\", handler.HandleMessage)`。\n5. `HandleMessage` 函式目前只需綁定請求的 JSON，並回傳一個固定的模擬回應，例如：`c.JSON(http.StatusOK, gin.H{\"reply\": \"這是一個模擬回應\"})`。", "testStrategy": "啟動後端伺服器。使用 Postman 或 curl 工具向 `http://localhost:8080/api/message` 發送 POST 請求，驗證是否能收到預設的 JSON 回應。", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 4, "title": "實作前端語音轉文字 (STT) 功能", "description": "在 React 應用中整合語音辨識 (STT) 功能，使用瀏覽器原生的 Web Speech API。", "details": "1. 建立一個自定義 Hook `src/hooks/useSpeechRecognition.ts` 來封裝 `SpeechRecognition` API 的邏輯。\n2. Hook 應提供 `isListening` (布林值)、`transcript` (字串)、`startListening` (函式)、`stopListening` (函式) 等介面。\n3. 在 `VoiceButton` 元件中引入此 Hook，實現按住按鈕開始辨識，放開按鈕停止辨識的互動。\n4. 辨識完成後，將 `transcript` 的結果更新到應用的狀態中。", "testStrategy": "在支援 Web Speech API 的瀏覽器（如 Chrome）中進行測試。點擊麥克風按鈕並說話，確認 `isListening` 狀態會改變，且辨識出的文字能正確地顯示在 UI 上。", "priority": "medium", "dependencies": [2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "串接前後端對話流程", "description": "將前端辨識出的文字，透過 API 發送到後端，並將後端的回應顯示在對話介面上。", "details": "1. 在 `src/services/api.ts` 中建立一個非同步函式 `sendMessage(message: string)`，使用 `fetch` 或 `axios` 向後端的 `/api/message` 發送 POST 請求。\n2. 在 React 主元件中，使用 `useState` 管理對話列表（一個物件陣列，如 `{sender: 'user' | 'ai', text: string}`）。\n3. 當 STT 辨識出結果後，先將使用者訊息加入對話列表，然後呼叫 `sendMessage`。 \n4. 收到後端回應後，再將 AI 的回應加入對話列表，觸發 UI 更新。", "testStrategy": "完成語音輸入後，檢查瀏覽器的網路請求分頁，確認已向後端發送請求。確認前端 UI 能正確顯示使用者輸入的文字和後端回傳的模擬回應。", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 6, "title": "後端串接真實 LLM API", "description": "修改後端服務，使其不再回傳模擬資料，而是實際呼叫外部 LLM API (如 Google Gemini 或 OpenAI GPT) 並回傳其結果。", "details": "1. 在 `internal/service/llm_service.go` 中建立與 LLM API 互動的邏輯。\n2. 使用 `net/http` 套件建立 HTTP 客戶端，向 LLM 的端點發送請求。\n3. 從環境變數或 `internal/config` 模組中安全地讀取 LLM API 金鑰。\n4. 在 `handler` 中呼叫 `llm_service`，將從前端收到的訊息轉發給 LLM。\n5. 解析 LLM API 的 JSON 回應，提取出主要的文字回覆，並將其封裝成 `{\"reply\": \"...\"}` 的格式回傳給前端。", "testStrategy": "使用 Postman 再次測試 `/api/message` 端點。這次傳入不同的問題，驗證後端是否能從真實的 LLM 服務獲取多樣化的、有意義的回應。", "priority": "medium", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 7, "title": "實作前端文字轉語音 (TTS) 功能", "description": "整合瀏覽器的 SpeechSynthesis API，將從後端收到的 LLM 文字回應轉換為語音並自動播放。", "details": "1. 建立一個工具函式 `src/utils/tts.ts`，封裝 `window.speechSynthesis` 的使用。\n2. 函式應接收一個字串作為參數，並執行 `const utterance = new SpeechSynthesisUtterance(text); window.speechSynthesis.speak(utterance);`。\n3. 在 React 主元件中，使用 `useEffect` 監聽對話列表的變化。\n4. 當偵測到有新的 AI 回應（`sender === 'ai'`）被加入列表時，呼叫 TTS 工具函式來朗讀該回應的文字內容。", "testStrategy": "完成一次完整的對話流程。在收到 AI 的文字回應後，驗證瀏覽器是否會自動播放對應的語音。可以嘗試中斷播放或連續對話，檢查其穩定性。", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 8, "title": "實作對話歷程 UI 渲染", "description": "將儲存在前端狀態中的對話歷史，以使用者和 AI 的對話泡泡形式，正確地渲染在 UI 上。", "details": "1. 在 `ChatHistory.tsx` 元件中，接收 `messages` 陣列作為 props。\n2. 使用 `.map()` 方法遍歷 `messages` 陣列。\n3. 根據每個 message 物件的 `sender` 屬性（'user' 或 'ai'），應用不同的 Tailwind CSS 樣式，例如 `justify-end` 用於使用者，`justify-start` 用於 AI。\n4. 確保對話容器有 `overflow-y-auto` 樣式，並在有新訊息時自動滾動到最底部。", "testStrategy": "進行多次對話，檢查對話歷史是否能正確累積。確認使用者和 AI 的對話泡泡樣式有明顯區分。驗證長對話是否可以滾動，以及新訊息出現時畫面是否會自動跟隨。", "priority": "low", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 9, "title": "完善前端 UI 狀態顯示", "description": "根據應用的即時狀態（如：閒置、聆聽中、處理中、播放中），在 UI 上提供明確的視覺回饋，提升使用者體驗。", "details": "1. 在 React 狀態中新增一個 `appStatus` 變數，其值可以是 `'idle'`, `'listening'`, `'processing'`, `'speaking'`。\n2. 在觸發 STT、發送 API 請求、接收回應、播放 TTS 的各個階段，更新此 `appStatus`。\n3. `VoiceButton` 和 `StatusIndicator` 元件根據 `appStatus` 的值顯示不同的圖示、顏色或提示文字。例如，`listening` 時麥克風圖示可以變色或加入動畫效果。", "testStrategy": "操作應用並觀察 UI 變化。確認在說話時，UI 會顯示「辨識中」。在等待 AI 回應時，UI 會顯示「傳送中」或「思考中」。在播放語音時，UI 有相應的提示。", "priority": "medium", "dependencies": [4, 5, 7], "status": "pending", "subtasks": []}, {"id": 10, "title": "錯誤處理與端對端整合測試", "description": "為整個應用流程添加穩健的錯誤處理機制，並進行端對端測試，確保所有模組能順利協同工作。", "details": "1. 前端：在 `fetch` API 呼叫、STT 和 TTS 的相關程式碼塊周圍使用 `try...catch` 結構。若發生錯誤，更新 UI 狀態以顯示友善的錯誤訊息。\n2. 後端：在呼叫 LLM API 時，檢查 HTTP 回應狀態碼。若非 200，則向前端回傳一個帶有錯誤訊息的 5xx 狀態碼。\n3. 測試：手動執行完整的對話流程。模擬網路中斷、提供無效的 LLM API 金鑰等場景，驗證錯誤處理是否如預期般觸發。", "testStrategy": "執行端對端測試計畫，覆蓋所有主要功能路徑和已知的邊界情況。驗證在各種正常和異常情況下，應用程式的行為都符合預期，不會崩潰。", "priority": "low", "dependencies": [6, 8, 9], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-07T00:32:18.223Z", "updated": "2025-07-07T00:32:18.223Z", "description": "Tasks for master context"}}}