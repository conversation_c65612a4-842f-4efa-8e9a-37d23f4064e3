package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// MessageRequest represents the incoming message request
type MessageRequest struct {
	Message string `json:"message" binding:"required"`
}

// MessageResponse represents the response to be sent back
type MessageResponse struct {
	Reply string `json:"reply"`
}

// HandleMessage processes incoming messages and returns a response
func HandleMessage(c *gin.Context) {
	var req MessageRequest

	// Bind JSON request to struct
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// For now, return a mock response
	// TODO: In Task 6, this will be replaced with actual LLM API call
	response := MessageResponse{
		Reply: "這是一個模擬回應。您說了：" + req.Message,
	}

	c.JSON(http.StatusOK, response)
}
