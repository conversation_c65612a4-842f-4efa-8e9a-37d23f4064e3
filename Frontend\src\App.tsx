import { useState, useEffect } from 'react'
import <PERSON>t<PERSON><PERSON><PERSON> from './components/ChatHistory'
import VoiceButton from './components/VoiceButton'
import StatusIndicator, { AppStatus } from './components/StatusIndicator'
import { useSpeechRecognition } from './hooks/useSpeechRecognition'
import { sendMessage } from './services/api'

interface Message {
  sender: 'user' | 'ai';
  text: string;
  timestamp?: Date;
}

function App() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [appStatus, setAppStatus] = useState<AppStatus>('idle');

  const {
    transcript,
    isListening,
    isSupported,
    startListening,
    stopListening,
    error
  } = useSpeechRecognition();

  const handleStartListening = () => {
    if (!isSupported) {
      alert('Speech recognition is not supported in this browser. Please use Chrome or Edge.');
      return;
    }
    startListening();
    setAppStatus('listening');
  };

  const handleStopListening = () => {
    stopListening();
    setAppStatus('idle');
  };

  // Handle transcript changes and send to backend
  useEffect(() => {
    if (transcript && !isListening) {
      // Add user message to chat history
      const userMessage: Message = {
        sender: 'user',
        text: transcript,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, userMessage]);

      // Send message to backend API
      handleSendMessage(transcript);
    }
  }, [transcript, isListening]);

  const handleSendMessage = async (message: string) => {
    setAppStatus('processing');

    try {
      const response = await sendMessage(message);

      // Add AI response to chat history
      const aiMessage: Message = {
        sender: 'ai',
        text: response.reply,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiMessage]);

      setAppStatus('idle');
    } catch (error) {
      console.error('Failed to send message:', error);

      // Add error message to chat history
      const errorMessage: Message = {
        sender: 'ai',
        text: '抱歉，發生錯誤，請稍後再試。',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);

      setAppStatus('idle');
    }
  };

  // Handle speech recognition errors
  useEffect(() => {
    if (error) {
      console.error('Speech recognition error:', error);
      setAppStatus('idle');
    }
  }, [error]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 p-4">
        <h1 className="text-xl font-semibold text-gray-800 text-center">
          語音對話助手
        </h1>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex flex-col max-w-4xl mx-auto w-full">
        {/* Status Indicator */}
        <StatusIndicator status={appStatus} />

        {/* Error Display */}
        {error && (
          <div className="mx-4 mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 text-sm">⚠️ {error}</p>
          </div>
        )}

        {/* Speech Recognition Support Warning */}
        {!isSupported && (
          <div className="mx-4 mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-600 text-sm">
              ⚠️ Speech recognition is not supported in this browser. Please use Chrome or Edge for the best experience.
            </p>
          </div>
        )}

        {/* Chat History Area */}
        <div className="flex-1 bg-white mx-4 rounded-lg shadow-sm border border-gray-200 mb-4">
          <ChatHistory messages={messages} />
        </div>

        {/* Voice Button Area */}
        <div className="relative">
          <VoiceButton
            isListening={isListening}
            onStartListening={handleStartListening}
            onStopListening={handleStopListening}
            disabled={appStatus === 'processing' || appStatus === 'speaking'}
          />
        </div>
      </main>

      {/* Footer */}
      <footer className="p-4 text-center text-sm text-gray-500">
        按住麥克風按鈕開始說話，放開停止錄音
      </footer>
    </div>
  )
}

export default App
