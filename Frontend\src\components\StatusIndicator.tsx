import React from 'react';

export type AppStatus = 'idle' | 'listening' | 'processing' | 'speaking';

interface StatusIndicatorProps {
  status: AppStatus;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status }) => {
  const getStatusConfig = (status: AppStatus) => {
    switch (status) {
      case 'listening':
        return {
          icon: '🎤',
          text: '正在聆聽...',
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
        };
      case 'processing':
        return {
          icon: '🤔',
          text: '思考中...',
          color: 'text-blue-500',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
        };
      case 'speaking':
        return {
          icon: '🔊',
          text: '播放中...',
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
        };
      case 'idle':
      default:
        return {
          icon: '💬',
          text: '準備就緒',
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <div className="flex justify-center p-4">
      <div
        className={`
          flex items-center space-x-2 px-4 py-2 rounded-full border
          ${config.bgColor} ${config.borderColor} ${config.color}
          transition-all duration-300
        `}
      >
        <span className="text-lg">{config.icon}</span>
        <span className="text-sm font-medium">{config.text}</span>
        {(status === 'listening' || status === 'processing') && (
          <div className="flex space-x-1">
            <div className="w-1 h-1 bg-current rounded-full animate-bounce"></div>
            <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StatusIndicator;
