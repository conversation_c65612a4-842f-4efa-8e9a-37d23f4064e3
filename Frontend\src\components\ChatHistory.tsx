import React from 'react';

interface Message {
  sender: 'user' | 'ai';
  text: string;
  timestamp?: Date;
}

interface ChatHistoryProps {
  messages: Message[];
}

const ChatHistory: React.FC<ChatHistoryProps> = ({ messages }) => {
  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages.length === 0 ? (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <div className="text-4xl mb-2">🤖</div>
            <p>Hello! How can I help you today?</p>
            <p className="text-sm mt-1">Click the microphone button to start talking</p>
          </div>
        </div>
      ) : (
        messages.map((message, index) => (
          <div
            key={index}
            className={`flex ${
              message.sender === 'user' ? 'justify-end' : 'justify-start'
            }`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.sender === 'user'
                  ? 'bg-blue-500 text-white rounded-br-none'
                  : 'bg-gray-200 text-gray-800 rounded-bl-none'
              }`}
            >
              <div className="flex items-start space-x-2">
                <span className="text-lg">
                  {message.sender === 'user' ? '🧑' : '🤖'}
                </span>
                <p className="text-sm">{message.text}</p>
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default ChatHistory;
