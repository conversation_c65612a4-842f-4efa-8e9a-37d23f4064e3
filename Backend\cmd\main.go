package main

import (
	"log"
	"voiceapp/backend/internal/handler"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// Initialize Gin engine
	r := gin.Default()

	// Configure CORS middleware
	config := cors.DefaultConfig()
	config.AllowOrigins = []string{
		"http://localhost:5173", // Vite dev server
		"http://localhost:3000", // Alternative React dev server
		"http://127.0.0.1:5173",
		"http://127.0.0.1:3000",
	}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	config.AllowCredentials = true

	r.Use(cors.New(config))

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.J<PERSON>(200, gin.H{
			"status": "ok",
			"message": "Voice App Backend is running",
		})
	})

	// API routes
	api := r.Group("/api")
	{
		api.POST("/message", handler.HandleMessage)
	}

	// Start server
	port := ":8080"
	log.Printf("Starting server on port %s", port)
	if err := r.Run(port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
