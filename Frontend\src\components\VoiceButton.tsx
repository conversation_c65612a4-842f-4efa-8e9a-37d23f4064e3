import React from 'react';

interface VoiceButtonProps {
  isListening: boolean;
  onStartListening: () => void;
  onStopListening: () => void;
  disabled?: boolean;
}

const VoiceButton: React.FC<VoiceButtonProps> = ({
  isListening,
  onStartListening,
  onStopListening,
  disabled = false,
}) => {
  const handleMouseDown = () => {
    if (!disabled && !isListening) {
      onStartListening();
    }
  };

  const handleMouseUp = () => {
    if (isListening) {
      onStopListening();
    }
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    handleMouseDown();
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault();
    handleMouseUp();
  };

  return (
    <div className="flex justify-center p-6">
      <button
        className={`
          w-20 h-20 rounded-full flex items-center justify-center text-white text-2xl
          transition-all duration-200 transform
          ${
            isListening
              ? 'bg-red-500 scale-110 animate-pulse shadow-lg shadow-red-500/50'
              : disabled
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600 hover:scale-105 shadow-lg hover:shadow-blue-500/50'
          }
          ${!disabled && !isListening ? 'active:scale-95' : ''}
        `}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        disabled={disabled}
        aria-label={isListening ? 'Stop recording' : 'Start recording'}
      >
        {isListening ? '🔴' : '🎤'}
      </button>
      <div className="absolute mt-24 text-center">
        <p className="text-sm text-gray-600">
          {isListening ? 'Release to stop' : 'Hold to speak'}
        </p>
      </div>
    </div>
  );
};

export default VoiceButton;
