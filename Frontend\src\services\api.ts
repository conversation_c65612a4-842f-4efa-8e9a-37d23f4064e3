const API_BASE_URL = 'http://localhost:8080';

export interface MessageRequest {
  message: string;
}

export interface MessageResponse {
  reply: string;
}

export interface ApiError {
  error: string;
  details?: string;
}

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData: ApiError = await response.json().catch(() => ({
          error: `HTTP ${response.status}: ${response.statusText}`,
        }));
        throw new Error(errorData.error || `Request failed with status ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('An unexpected error occurred');
    }
  }

  async sendMessage(message: string): Promise<MessageResponse> {
    const requestBody: MessageRequest = { message };
    
    return this.request<MessageResponse>('/api/message', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });
  }

  async healthCheck(): Promise<{ status: string; message: string }> {
    return this.request<{ status: string; message: string }>('/health', {
      method: 'GET',
    });
  }
}

// Export a singleton instance
export const apiService = new ApiService();

// Export the sendMessage function for convenience
export const sendMessage = (message: string) => apiService.sendMessage(message);
